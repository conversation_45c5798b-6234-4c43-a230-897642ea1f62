export const CHAT_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'text/plain', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ],
  MESSAGES_PER_PAGE: 50,
  TYPING_TIMEOUT: 3000,
  RECONNECT_INTERVAL: 5000,
} as const;

export const MESSAGE_TYPES = {
  TEXT: 'text',
  FILE: 'file',
  IMAGE: 'image',
} as const;

export const CHAT_ROUTES = {
  CHAT: '/chat',
  CONVERSATION: (id: string) => `/chat/${id}`,
} as const;
