import { createClient } from '@/lib/supabase/server';
import { Message, SendMessageRequest } from '../types';

export async function getMessages(
  conversationId: string,
  userId: string,
  limit = 50,
  offset = 0
): Promise<{ data: Message[] | null; error: string | null }> {
  try {
    const supabase = await createClient();

    // Verify user is participant in conversation
    const { data: participant } = await supabase
      .from('conversation_participants')
      .select('id')
      .eq('conversation_id', conversationId)
      .eq('user_id', userId)
      .single();

    if (!participant) {
      return { data: null, error: 'Access denied' };
    }

    // Fetch messages with sender info
    const { data: messages, error } = await supabase
      .from('messages')
      .select(`
        id, conversation_id, sender_id, content, message_type, created_at, updated_at,
        sender:users!sender_id(id, first_name, last_name, avatar_url),
        attachments:message_attachments(*)
      `)
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      return { data: null, error: error.message };
    }

    return { data: messages || [], error: null };
  } catch (error) {
    return { data: null, error: 'Failed to fetch messages' };
  }
}

export async function sendMessage(
  request: SendMessageRequest,
  userId: string
): Promise<{ data: Message | null; error: string | null }> {
  try {
    const supabase = await createClient();

    // Verify user is participant
    const { data: participant } = await supabase
      .from('conversation_participants')
      .select('id')
      .eq('conversation_id', request.conversation_id)
      .eq('user_id', userId)
      .single();

    if (!participant) {
      return { data: null, error: 'Access denied' };
    }

    // Insert message
    const { data: message, error } = await supabase
      .from('messages')
      .insert({
        conversation_id: request.conversation_id,
        sender_id: userId,
        content: request.content,
        message_type: request.message_type,
      })
      .select(`
        id, conversation_id, sender_id, content, message_type, created_at, updated_at,
        sender:users!sender_id(id, first_name, last_name, avatar_url)
      `)
      .single();

    if (error) {
      return { data: null, error: error.message };
    }

    return { data: message, error: null };
  } catch (error) {
    return { data: null, error: 'Failed to send message' };
  }
}
